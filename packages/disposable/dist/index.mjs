if (!globalThis['garbage']) {
    globalThis['garbage'] = new WeakMap();
}
class Disposable {
    constructor() {
        this._toDispose = new Set();
    }
    dispose() {
        globalThis['garbage'].set(this, this.identifier);
        for (let d of this._toDispose.values()) {
            d.dispose();
        }
        this._toDispose.clear();
    }
    _register(t) {
        if (t == this) {
            throw new Error('Cannot register a disposable on itself');
        }
        this._toDispose.add(t);
    }
    get identifier() {
        return '';
    }
}

export { Disposable };
//# sourceMappingURL=index.mjs.map
